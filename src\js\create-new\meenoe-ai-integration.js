/**
 * Meenoe AI Integration Layer
 * Provides AI assistant with comprehensive access to all Meenoe systems
 */

class MeenoeAIIntegration {
    constructor() {
        this.functions = new Map();
        this.contextCache = new Map();
        this.eventListeners = new Map();
        
        this.initializeFunctions();
        this.setupEventListeners();
        
        console.log('🔗 Meenoe AI Integration initialized');
    }

    initializeFunctions() {
        // Register all available functions for AI
        this.registerStateManagementFunctions();
        this.registerAgendaFlowFunctions();
        this.registerActionManagementFunctions();
        this.registerInitializationFunctions();
        this.registerUtilityFunctions();
    }

    registerStateManagementFunctions() {
        // Meenoe State Management Functions
        this.registerFunction({
            name: 'getCurrentMeenoeState',
            description: 'Get the complete current state of the Meenoe session including counters, participants, agenda items, and actions',
            parameters: {
                type: 'object',
                properties: {},
                required: []
            },
            handler: () => this.getCurrentMeenoeState()
        });

        this.registerFunction({
            name: 'updateUserCount',
            description: 'Update the participant count in the Meenoe session',
            parameters: {
                type: 'object',
                properties: {
                    count: { type: 'number', description: 'New participant count' }
                },
                required: ['count']
            },
            handler: (params) => this.updateUserCount(params.count)
        });

        this.registerFunction({
            name: 'updateAgendaCount',
            description: 'Update the agenda items count',
            parameters: {
                type: 'object',
                properties: {
                    count: { type: 'number', description: 'New agenda count' }
                },
                required: ['count']
            },
            handler: (params) => this.updateAgendaCount(params.count)
        });

        this.registerFunction({
            name: 'updateFileCount',
            description: 'Update the file attachments count',
            parameters: {
                type: 'object',
                properties: {
                    count: { type: 'number', description: 'New file count' }
                },
                required: ['count']
            },
            handler: (params) => this.updateFileCount(params.count)
        });

        this.registerFunction({
            name: 'updateActionCount',
            description: 'Update the action items count',
            parameters: {
                type: 'object',
                properties: {
                    count: { type: 'number', description: 'New action count' }
                },
                required: ['count']
            },
            handler: (params) => this.updateActionCount(params.count)
        });

        this.registerFunction({
            name: 'refreshAllCounters',
            description: 'Refresh and sync all counters with current system state',
            parameters: {
                type: 'object',
                properties: {},
                required: []
            },
            handler: () => this.refreshAllCounters()
        });
    }

    registerAgendaFlowFunctions() {
        // Agenda Flow Management Functions
        this.registerFunction({
            name: 'createAgendaPoint',
            description: 'Create a new agenda item with title, description, and urgency level',
            parameters: {
                type: 'object',
                properties: {
                    title: { type: 'string', description: 'Agenda item title' },
                    description: { type: 'string', description: 'Detailed description of the agenda item' },
                    urgency: { type: 'string', enum: ['normal', 'moderate', 'important', 'critical', 'mandatory'], description: 'Urgency level (normal/moderate/important/critical/mandatory)' }
                },
                required: ['title']
            },
            handler: (params) => this.createAgendaPoint(params.title, params.description, params.urgency)
        });

        this.registerFunction({
            name: 'updateAgendaPoint',
            description: 'Update an existing agenda item',
            parameters: {
                type: 'object',
                properties: {
                    id: { type: 'string', description: 'Agenda item ID' },
                    updates: { 
                        type: 'object', 
                        description: 'Object containing fields to update',
                        properties: {
                            title: { type: 'string' },
                            description: { type: 'string' },
                            urgency: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] }
                        }
                    }
                },
                required: ['id', 'updates']
            },
            handler: (params) => this.updateAgendaPoint(params.id, params.updates)
        });

        this.registerFunction({
            name: 'deleteAgendaPoint',
            description: 'Remove an agenda item from the meeting',
            parameters: {
                type: 'object',
                properties: {
                    id: { type: 'string', description: 'Agenda item ID to delete' }
                },
                required: ['id']
            },
            handler: (params) => this.deleteAgendaPoint(params.id)
        });

        this.registerFunction({
            name: 'reorderAgendaPoints',
            description: 'Reorder agenda items by providing new order array',
            parameters: {
                type: 'object',
                properties: {
                    newOrder: { 
                        type: 'array', 
                        items: { type: 'string' },
                        description: 'Array of agenda item IDs in new order' 
                    }
                },
                required: ['newOrder']
            },
            handler: (params) => this.reorderAgendaPoints(params.newOrder)
        });

        this.registerFunction({
            name: 'addThreadToAgenda',
            description: 'Add a discussion thread to an agenda item',
            parameters: {
                type: 'object',
                properties: {
                    agendaId: { type: 'string', description: 'Agenda item ID' },
                    content: { type: 'string', description: 'Thread content' },
                    author: { type: 'string', description: 'Thread author name' }
                },
                required: ['agendaId', 'content', 'author']
            },
            handler: (params) => this.addThreadToAgenda(params.agendaId, params.content, params.author)
        });

        this.registerFunction({
            name: 'setAgendaUrgency',
            description: 'Set the urgency level for an agenda item',
            parameters: {
                type: 'object',
                properties: {
                    id: { type: 'string', description: 'Agenda item ID' },
                    urgency: { type: 'string', enum: ['normal', 'moderate', 'important', 'critical', 'mandatory'], description: 'New urgency level' }
                },
                required: ['id', 'urgency']
            },
            handler: (params) => this.setAgendaUrgency(params.id, params.urgency)
        });

        this.registerFunction({
            name: 'linkAgendaToAction',
            description: 'Create a link between an agenda item and an action item',
            parameters: {
                type: 'object',
                properties: {
                    agendaId: { type: 'string', description: 'Agenda item ID' },
                    actionId: { type: 'string', description: 'Action item ID' }
                },
                required: ['agendaId', 'actionId']
            },
            handler: (params) => this.linkAgendaToAction(params.agendaId, params.actionId)
        });

        this.registerFunction({
            name: 'getAgendaAnalytics',
            description: 'Get analytics and metrics about agenda items and discussions',
            parameters: {
                type: 'object',
                properties: {},
                required: []
            },
            handler: () => this.getAgendaAnalytics()
        });
    }

    registerActionManagementFunctions() {
        // Action Management Functions
        this.registerFunction({
            name: 'createAction',
            description: 'Create a new action item with title and optional type',
            parameters: {
                type: 'object',
                properties: {
                    title: { type: 'string', description: 'Action item title' },
                    actionType: { type: 'string', enum: ['Approvals', 'Surveys', 'Signing', 'Voting', 'Feedback', 'Brainstorming', 'Scheduling', 'Reporting', 'Video', 'Audio', 'Presentation', 'Integrations', 'Custom Action'], description: 'Type of action' },
                    assignee: { type: 'string', description: 'Person assigned to the action (user name)' },
                    actionDate: { type: 'string', description: 'Due date in ISO format' }
                },
                required: ['title']
            },
            handler: (params) => this.createAction(params.title, params.actionType, params.assignee, params.actionDate)
        });

        this.registerFunction({
            name: 'updateAction',
            description: 'Update an existing action item',
            parameters: {
                type: 'object',
                properties: {
                    id: { type: 'string', description: 'Action item ID' },
                    updates: {
                        type: 'object',
                        description: 'Object containing fields to update',
                        properties: {
                            actionTitle: { type: 'string', description: 'Action title' },
                            actionType: { type: 'string', enum: ['Approvals', 'Surveys', 'Signing', 'Voting', 'Feedback', 'Brainstorming', 'Scheduling', 'Reporting', 'Video', 'Audio', 'Presentation', 'Integrations', 'Custom Action'], description: 'Action type' },
                            actionUsers: { type: 'array', items: { type: 'string' }, description: 'Array of assigned users' },
                            actionDate: { type: 'string', description: 'Due date in ISO format' },
                            actionStatus: { type: 'string', enum: ['open', 'complete', 'queued'], description: 'Action status' }
                        }
                    }
                },
                required: ['id', 'updates']
            },
            handler: (params) => this.updateAction(params.id, params.updates)
        });

        this.registerFunction({
            name: 'deleteAction',
            description: 'Remove an action item',
            parameters: {
                type: 'object',
                properties: {
                    id: { type: 'string', description: 'Action item ID to delete' }
                },
                required: ['id']
            },
            handler: (params) => this.deleteAction(params.id)
        });

        this.registerFunction({
            name: 'assignAction',
            description: 'Assign an action item to a specific user',
            parameters: {
                type: 'object',
                properties: {
                    actionId: { type: 'string', description: 'Action item ID' },
                    userId: { type: 'string', description: 'User ID to assign to' }
                },
                required: ['actionId', 'userId']
            },
            handler: (params) => this.assignAction(params.actionId, params.userId)
        });

        this.registerFunction({
            name: 'setActionStatus',
            description: 'Update the status of an action item',
            parameters: {
                type: 'object',
                properties: {
                    id: { type: 'string', description: 'Action item ID' },
                    status: { type: 'string', enum: ['open', 'in-progress', 'complete', 'blocked'], description: 'New status' }
                },
                required: ['id', 'status']
            },
            handler: (params) => this.setActionStatus(params.id, params.status)
        });

        this.registerFunction({
            name: 'createSubAction',
            description: 'Create a sub-action under a parent action item',
            parameters: {
                type: 'object',
                properties: {
                    parentId: { type: 'string', description: 'Parent action item ID' },
                    details: { 
                        type: 'object', 
                        description: 'Sub-action details',
                        properties: {
                            title: { type: 'string' },
                            description: { type: 'string' },
                            assignee: { type: 'string' },
                            dueDate: { type: 'string' }
                        },
                        required: ['title']
                    }
                },
                required: ['parentId', 'details']
            },
            handler: (params) => this.createSubAction(params.parentId, params.details)
        });

        this.registerFunction({
            name: 'linkActionToAgenda',
            description: 'Link an action item to an agenda item',
            parameters: {
                type: 'object',
                properties: {
                    actionId: { type: 'string', description: 'Action item ID' },
                    agendaId: { type: 'string', description: 'Agenda item ID' }
                },
                required: ['actionId', 'agendaId']
            },
            handler: (params) => this.linkActionToAgenda(params.actionId, params.agendaId)
        });

        this.registerFunction({
            name: 'getActionAnalytics',
            description: 'Get analytics and metrics about action items and completion rates',
            parameters: {
                type: 'object',
                properties: {},
                required: []
            },
            handler: () => this.getActionAnalytics()
        });
    }

    registerInitializationFunctions() {
        // Meenoe Initialization Functions
        this.registerFunction({
            name: 'initializeMeenoe',
            description: 'Initialize a new Meenoe session with configuration',
            parameters: {
                type: 'object',
                properties: {
                    config: {
                        type: 'object',
                        description: 'Meenoe configuration object',
                        properties: {
                            title: { type: 'string' },
                            objective: { type: 'string' },
                            participants: { type: 'array', items: { type: 'string' } }
                        }
                    }
                },
                required: ['config']
            },
            handler: (params) => this.initializeMeenoe(params.config)
        });

        this.registerFunction({
            name: 'updateMeenoeTitle',
            description: 'Update the title of the current Meenoe session',
            parameters: {
                type: 'object',
                properties: {
                    title: { type: 'string', description: 'New meeting title' }
                },
                required: ['title']
            },
            handler: (params) => this.updateMeenoeTitle(params.title)
        });

        this.registerFunction({
            name: 'updateMeenoeObjective',
            description: 'Update the objective/purpose of the current Meenoe session',
            parameters: {
                type: 'object',
                properties: {
                    objective: { type: 'string', description: 'New meeting objective' }
                },
                required: ['objective']
            },
            handler: (params) => this.updateMeenoeObjective(params.objective)
        });

        this.registerFunction({
            name: 'addParticipant',
            description: 'Add a participant to the Meenoe session',
            parameters: {
                type: 'object',
                properties: {
                    userDetails: {
                        type: 'object',
                        description: 'Participant details',
                        properties: {
                            name: { type: 'string' },
                            email: { type: 'string' },
                            role: { type: 'string' }
                        },
                        required: ['name']
                    }
                },
                required: ['userDetails']
            },
            handler: (params) => this.addParticipant(params.userDetails)
        });

        this.registerFunction({
            name: 'removeParticipant',
            description: 'Remove a participant from the Meenoe session',
            parameters: {
                type: 'object',
                properties: {
                    userId: { type: 'string', description: 'User ID to remove' }
                },
                required: ['userId']
            },
            handler: (params) => this.removeParticipant(params.userId)
        });

        this.registerFunction({
            name: 'setMeenoeStatus',
            description: 'Update the publishing status of the Meenoe session',
            parameters: {
                type: 'object',
                properties: {
                    status: { type: 'string', enum: ['draft', 'live', 'completed', 'archived'], description: 'New status' }
                },
                required: ['status']
            },
            handler: (params) => this.setMeenoeStatus(params.status)
        });

        this.registerFunction({
            name: 'configureMeenoeSettings',
            description: 'Configure various Meenoe session settings',
            parameters: {
                type: 'object',
                properties: {
                    settings: {
                        type: 'object',
                        description: 'Settings object',
                        properties: {
                            allowPublicAccess: { type: 'boolean' },
                            enableNotifications: { type: 'boolean' },
                            autoSave: { type: 'boolean' }
                        }
                    }
                },
                required: ['settings']
            },
            handler: (params) => this.configureMeenoeSettings(params.settings)
        });
    }

    registerUtilityFunctions() {
        // Utility Functions
        this.registerFunction({
            name: 'analyzeMeetingStructure',
            description: 'Analyze the current meeting structure and provide optimization suggestions',
            parameters: {
                type: 'object',
                properties: {},
                required: []
            },
            handler: () => this.analyzeMeetingStructure()
        });

        this.registerFunction({
            name: 'generateMeetingSummary',
            description: 'Generate a comprehensive summary of the current meeting state',
            parameters: {
                type: 'object',
                properties: {},
                required: []
            },
            handler: () => this.generateMeetingSummary()
        });

        this.registerFunction({
            name: 'extractActionsFromText',
            description: 'Extract potential action items from provided text',
            parameters: {
                type: 'object',
                properties: {
                    text: { type: 'string', description: 'Text to analyze for action items' }
                },
                required: ['text']
            },
            handler: (params) => this.extractActionsFromText(params.text)
        });

        this.registerFunction({
            name: 'suggestAgendaStructure',
            description: 'Suggest an optimal agenda structure based on meeting objective and participants',
            parameters: {
                type: 'object',
                properties: {
                    objective: { type: 'string', description: 'Meeting objective' },
                    participantCount: { type: 'number', description: 'Number of participants' },
                    duration: { type: 'number', description: 'Meeting duration in minutes' }
                },
                required: ['objective']
            },
            handler: (params) => this.suggestAgendaStructure(params.objective, params.participantCount, params.duration)
        });
    }

    registerFunction(functionDef) {
        this.functions.set(functionDef.name, functionDef);
    }

    getFunctionDefinitions() {
        return Array.from(this.functions.values()).map(func => ({
            name: func.name,
            description: func.description,
            parameters: func.parameters
        }));
    }

    async executeFunction(functionName, parameters) {
        const func = this.functions.get(functionName);
        if (!func) {
            throw new Error(`Function ${functionName} not found`);
        }

        try {
            console.log(`🔧 Executing function: ${functionName}`, parameters);
            const result = await func.handler(parameters);
            console.log(`✅ Function ${functionName} completed`, result);
            return result;
        } catch (error) {
            console.error(`❌ Function ${functionName} failed:`, error);
            throw error;
        }
    }

    setupEventListeners() {
        // Listen for Meenoe system events
        window.addEventListener('meenoeStateChanged', (event) => {
            this.handleStateChange(event.detail);
        });

        window.addEventListener('meenoeAgendaChanged', (event) => {
            this.handleAgendaChange(event.detail);
        });

        window.addEventListener('meenoeActionsChanged', (event) => {
            this.handleActionsChange(event.detail);
        });

        window.addEventListener('meenoeUsersChanged', (event) => {
            this.handleUsersChange(event.detail);
        });
    }

    // State Management Function Implementations
    getCurrentMeenoeState() {
        // Check if meenoe state system is available
        if (!window.meenoeState) {
            console.warn('Meenoe state system not available, returning empty state');
            return this.getEmptyMeenoeState();
        }

        try {
            return {
                ...window.meenoeState.state,
                timestamp: new Date().toISOString(),
                systemStatus: {
                    agendaFlowAvailable: !!window.agendaFlow,
                    actionsAvailable: !!window.tree,
                    usersAvailable: !!window.meenoeUsers,
                    integrationsAvailable: !!window.meenoeIntegrations
                }
            };
        } catch (error) {
            console.warn('Error getting Meenoe state:', error);
            return this.getEmptyMeenoeState();
        }
    }

    getEmptyMeenoeState() {
        return {
            name: '',
            objective: '',
            agenda: [],
            actions: [],
            users: [],
            files: [],
            timestamp: new Date().toISOString(),
            systemStatus: {
                agendaFlowAvailable: false,
                actionsAvailable: false,
                usersAvailable: false,
                integrationsAvailable: false
            }
        };
    }

    updateUserCount(count) {
        if (!window.meenoeState) {
            throw new Error('Meenoe state system not available');
        }

        window.meenoeState.updateUserCount(count);
        return { success: true, newCount: count };
    }

    updateAgendaCount(count) {
        if (!window.meenoeState) {
            throw new Error('Meenoe state system not available');
        }

        window.meenoeState.updateAgendaCount(count);
        return { success: true, newCount: count };
    }

    updateFileCount(count) {
        if (!window.meenoeState) {
            throw new Error('Meenoe state system not available');
        }

        window.meenoeState.updateFileCount(count);
        return { success: true, newCount: count };
    }

    updateActionCount(count) {
        if (!window.meenoeState) {
            throw new Error('Meenoe state system not available');
        }

        window.meenoeState.updateActionCount(count);
        return { success: true, newCount: count };
    }

    refreshAllCounters() {
        if (!window.meenoeState) {
            throw new Error('Meenoe state system not available');
        }

        window.meenoeState.refreshAllCounters();
        return { success: true, counters: window.meenoeState.state.counters };
    }

    // Agenda Flow Function Implementations
    createAgendaPoint(title, description = '', urgency = 'normal') {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        try {
            // Use the proper AgendaFlow method to create the agenda point
            window.agendaFlow.addNewAgendaPoint(title, description);

            // Get the newly created agenda item
            const agendaItems = Array.from(window.agendaFlow.state.agendaItems.values());
            const newItem = agendaItems[agendaItems.length - 1];

            if (newItem && urgency !== 'normal') {
                // Update the urgency in the state
                newItem.urgency = urgency;
                window.agendaFlow.state.agendaItems.set(newItem.id, newItem);

                // Update the UI urgency display for the specific item
                window.agendaFlow.updateAgendaItemUI(newItem.id);

                // Also update the background color
                const urgencyLevel = window.agendaFlow.state.urgencyLevels.find(level => level.id === urgency);
                if (urgencyLevel) {
                    window.agendaFlow.updateAgendaItemBackground(newItem.id, urgencyLevel.bgClass);
                }
            }

            return {
                success: true,
                agendaId: newItem?.id,
                title: title,
                description: description,
                urgency: urgency
            };
        } catch (error) {
            throw new Error(`Failed to create agenda point: ${error.message}`);
        }
    }

    updateAgendaPoint(id, updates) {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        const agendaItem = window.agendaFlow.state.agendaItems.get(id);
        if (!agendaItem) {
            throw new Error(`Agenda item ${id} not found`);
        }

        try {
            // Update the agenda item in state
            Object.assign(agendaItem, updates);
            window.agendaFlow.state.agendaItems.set(id, agendaItem);

            // Update UI if element exists
            const agendaElement = document.querySelector(`[data-agenda-id="${id}"]`);
            if (agendaElement) {
                if (updates.title) {
                    const titleElement = agendaElement.querySelector('.meenoe-ed-title');
                    if (titleElement) titleElement.textContent = updates.title;
                }

                if (updates.description) {
                    const descElement = agendaElement.querySelector('.meenoe-ed-description');
                    if (descElement) descElement.textContent = updates.description;
                }

                if (updates.urgency) {
                    this.setAgendaUrgency(id, updates.urgency);
                }
            }

            return { success: true, updatedItem: agendaItem };
        } catch (error) {
            throw new Error(`Failed to update agenda point: ${error.message}`);
        }
    }

    deleteAgendaPoint(id) {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        try {
            // Remove from state
            const deleted = window.agendaFlow.state.agendaItems.delete(id);

            if (!deleted) {
                throw new Error(`Agenda item ${id} not found`);
            }

            // Remove from UI
            const agendaElement = document.querySelector(`[data-agenda-id="${id}"]`);
            if (agendaElement) {
                agendaElement.remove();
                window.agendaFlow.renumberAgendaPoints();
            }

            return { success: true, deletedId: id };
        } catch (error) {
            throw new Error(`Failed to delete agenda point: ${error.message}`);
        }
    }

    reorderAgendaPoints(newOrder) {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        try {
            const container = document.getElementById('all-agenda-points');
            if (!container) {
                throw new Error('Agenda container not found');
            }

            // Reorder DOM elements
            newOrder.forEach((agendaId, index) => {
                const element = container.querySelector(`[data-agenda-id="${agendaId}"]`);
                if (element) {
                    container.appendChild(element);
                }
            });

            window.agendaFlow.renumberAgendaPoints();
            return { success: true, newOrder: newOrder };
        } catch (error) {
            throw new Error(`Failed to reorder agenda points: ${error.message}`);
        }
    }

    addThreadToAgenda(agendaId, content, author) {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        const agendaItem = window.agendaFlow.state.agendaItems.get(agendaId);
        if (!agendaItem) {
            throw new Error(`Agenda item ${agendaId} not found`);
        }

        try {
            const thread = {
                id: `thread-${Date.now()}`,
                content: content,
                author: author,
                timestamp: new Date().toISOString()
            };

            if (!agendaItem.threads) {
                agendaItem.threads = [];
            }

            agendaItem.threads.push(thread);
            window.agendaFlow.state.agendaItems.set(agendaId, agendaItem);

            return { success: true, threadId: thread.id, agendaId: agendaId };
        } catch (error) {
            throw new Error(`Failed to add thread to agenda: ${error.message}`);
        }
    }

    editAgendaThread(agendaId, threadId, content) {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        const agendaItem = window.agendaFlow.state.agendaItems.get(agendaId);
        if (!agendaItem) {
            throw new Error(`Agenda item ${agendaId} not found`);
        }

        if (!agendaItem.threads) {
            throw new Error('No threads found for this agenda item');
        }

        const thread = agendaItem.threads.find(t => t.id === threadId);
        if (!thread) {
            throw new Error(`Thread ${threadId} not found`);
        }

        try {
            thread.content = content;
            thread.isEditing = false;
            window.agendaFlow.state.agendaItems.set(agendaId, agendaItem);

            return { success: true, threadId: threadId, agendaId: agendaId };
        } catch (error) {
            throw new Error(`Failed to edit thread: ${error.message}`);
        }
    }

    deleteAgendaThread(agendaId, threadId) {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        const agendaItem = window.agendaFlow.state.agendaItems.get(agendaId);
        if (!agendaItem) {
            throw new Error(`Agenda item ${agendaId} not found`);
        }

        if (!agendaItem.threads) {
            throw new Error('No threads found for this agenda item');
        }

        try {
            agendaItem.threads = agendaItem.threads.filter(t => t.id !== threadId);
            window.agendaFlow.state.agendaItems.set(agendaId, agendaItem);

            return { success: true, threadId: threadId, agendaId: agendaId };
        } catch (error) {
            throw new Error(`Failed to delete thread: ${error.message}`);
        }
    }

    setAgendaUrgency(id, urgency) {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        const agendaItem = window.agendaFlow.state.agendaItems.get(id);
        if (!agendaItem) {
            throw new Error(`Agenda item ${id} not found`);
        }

        try {
            agendaItem.urgency = urgency;
            window.agendaFlow.state.agendaItems.set(id, agendaItem);

            // Update UI urgency display using the proper AgendaFlow method
            window.agendaFlow.updateAgendaItemUI(id);

            // Also update the background color
            const urgencyLevel = window.agendaFlow.state.urgencyLevels.find(level => level.id === urgency);
            if (urgencyLevel) {
                window.agendaFlow.updateAgendaItemBackground(id, urgencyLevel.bgClass);
            }

            return { success: true, agendaId: id, urgency: urgency };
        } catch (error) {
            throw new Error(`Failed to set agenda urgency: ${error.message}`);
        }
    }

    linkAgendaToAction(agendaId, actionId) {
        if (!window.agendaFlow || !window.tree) {
            throw new Error('AgendaFlow or Actions system not available');
        }

        try {
            // Find the action node
            const actionNode = this.findActionNode(window.tree, actionId);
            if (!actionNode) {
                throw new Error(`Action ${actionId} not found`);
            }

            // Link agenda to action
            if (!actionNode.linkedAgenda) {
                actionNode.linkedAgenda = [];
            }

            if (!actionNode.linkedAgenda.some(link => link.id === agendaId)) {
                actionNode.linkedAgenda.push({ id: agendaId });
            }

            return { success: true, agendaId: agendaId, actionId: actionId };
        } catch (error) {
            throw new Error(`Failed to link agenda to action: ${error.message}`);
        }
    }

    getAgendaAnalytics() {
        if (!window.agendaFlow) {
            throw new Error('AgendaFlow system not available');
        }

        const agendaItems = Array.from(window.agendaFlow.state.agendaItems.values());

        const analytics = {
            totalItems: agendaItems.length,
            urgencyDistribution: {
                normal: agendaItems.filter(item => item.urgency === 'normal').length,
                moderate: agendaItems.filter(item => item.urgency === 'moderate').length,
                important: agendaItems.filter(item => item.urgency === 'important').length,
                critical: agendaItems.filter(item => item.urgency === 'critical').length,
                mandatory: agendaItems.filter(item => item.urgency === 'mandatory').length
            },
            totalThreads: agendaItems.reduce((sum, item) => sum + (item.threads?.length || 0), 0),
            averageThreadsPerItem: agendaItems.length > 0 ?
                agendaItems.reduce((sum, item) => sum + (item.threads?.length || 0), 0) / agendaItems.length : 0,
            itemsWithFiles: agendaItems.filter(item => item.files && item.files.length > 0).length,
            createdToday: agendaItems.filter(item => {
                const created = new Date(item.createdAt);
                const today = new Date();
                return created.toDateString() === today.toDateString();
            }).length
        };

        return analytics;
    }

    // Action Management Function Implementations
    createAction(title, actionType = null, assignee = '', actionDate = null) {
        if (!window.tree) {
            throw new Error('Actions system not available');
        }

        try {
            console.log('🔧 Creating action node with title:', title);

            // Create the node using the correct parameters
            // createNode(p_text, p_expanded, p_icon, p_parentNode, p_tag, p_contextmenu)
            const newNode = window.tree.createNode(title, false, null, null, null, 'context1');

            if (newNode) {
                console.log('✅ Action node created:', newNode.id);

                // Set additional properties using the correct property names from meenoeactions.js
                if (actionType) newNode.actionType = actionType;
                if (assignee) {
                    // Ensure actionUsers is an array
                    newNode.actionUsers = Array.isArray(assignee) ? assignee : [assignee];
                }
                if (actionDate) newNode.actionDate = actionDate;

                // The createNode function already adds the node to childNodes array
                // Just redraw tree to show changes
                window.tree.drawTree();

                console.log('✅ Action tree redrawn, total nodes:', window.tree.childNodes.length);

                return {
                    success: true,
                    actionId: newNode.id,
                    actionTitle: title,
                    actionType: actionType,
                    actionUsers: newNode.actionUsers,
                    actionDate: actionDate,
                    actionStatus: newNode.actionStatus
                };
            } else {
                throw new Error('Failed to create action node - createNode returned null');
            }
        } catch (error) {
            console.error('❌ Error in createAction:', error);
            throw new Error(`Failed to create action: ${error.message}`);
        }
    }

    updateAction(id, updates) {
        if (!window.tree) {
            throw new Error('Actions system not available');
        }

        try {
            const actionNode = this.findActionNode(window.tree, id);
            if (!actionNode) {
                throw new Error(`Action ${id} not found`);
            }

            // Update properties
            if (updates.title) actionNode.actionTitle = updates.title;
            if (updates.description) actionNode.actionDescription = updates.description;
            if (updates.assignee) actionNode.actionUsers = [updates.assignee];
            if (updates.dueDate) actionNode.actionDate = updates.dueDate;
            if (updates.priority) actionNode.actionPriority = updates.priority;
            if (updates.status) actionNode.actionStatus = updates.status;

            // Redraw tree to show changes
            window.tree.drawTree();

            return { success: true, actionId: id, updates: updates };
        } catch (error) {
            throw new Error(`Failed to update action: ${error.message}`);
        }
    }

    deleteAction(id) {
        if (!window.tree) {
            throw new Error('Actions system not available');
        }

        try {
            const actionNode = this.findActionNode(window.tree, id);
            if (!actionNode) {
                throw new Error(`Action ${id} not found`);
            }

            actionNode.removeNode();
            return { success: true, deletedId: id };
        } catch (error) {
            throw new Error(`Failed to delete action: ${error.message}`);
        }
    }

    assignAction(actionId, userId) {
        if (!window.tree) {
            throw new Error('Actions system not available');
        }

        try {
            const actionNode = this.findActionNode(window.tree, actionId);
            if (!actionNode) {
                throw new Error(`Action ${actionId} not found`);
            }

            actionNode.actionUsers = [userId];
            window.tree.drawTree();

            return { success: true, actionId: actionId, assignee: userId };
        } catch (error) {
            throw new Error(`Failed to assign action: ${error.message}`);
        }
    }

    setActionStatus(id, status) {
        if (!window.tree) {
            throw new Error('Actions system not available');
        }

        try {
            const actionNode = this.findActionNode(window.tree, id);
            if (!actionNode) {
                throw new Error(`Action ${id} not found`);
            }

            window.tree.updateNodeStatus(actionNode, status);
            return { success: true, actionId: id, status: status };
        } catch (error) {
            throw new Error(`Failed to set action status: ${error.message}`);
        }
    }

    createSubAction(parentId, details) {
        if (!window.tree) {
            throw new Error('Actions system not available');
        }

        try {
            const parentNode = this.findActionNode(window.tree, parentId);
            if (!parentNode) {
                throw new Error(`Parent action ${parentId} not found`);
            }

            const subAction = window.tree.createNode(
                details.title,
                false,
                null,
                parentNode,
                null,
                'context1'
            );

            if (subAction) {
                if (details.description) subAction.actionDescription = details.description;
                if (details.assignee) subAction.actionUsers = [details.assignee];
                if (details.dueDate) subAction.actionDate = details.dueDate;

                window.tree.drawTree();

                return {
                    success: true,
                    subActionId: subAction.id,
                    parentId: parentId,
                    details: details
                };
            } else {
                throw new Error('Failed to create sub-action');
            }
        } catch (error) {
            throw new Error(`Failed to create sub-action: ${error.message}`);
        }
    }

    linkActionToAgenda(actionId, agendaId) {
        return this.linkAgendaToAction(agendaId, actionId);
    }

    getActionAnalytics() {
        if (!window.tree) {
            throw new Error('Actions system not available');
        }

        const allActions = this.getAllActionNodes(window.tree);

        const analytics = {
            totalActions: allActions.length,
            statusDistribution: {
                open: allActions.filter(action => action.actionStatus === 'open').length,
                'in-progress': allActions.filter(action => action.actionStatus === 'in-progress').length,
                complete: allActions.filter(action => action.actionStatus === 'complete').length,
                blocked: allActions.filter(action => action.actionStatus === 'blocked').length
            },
            assignedActions: allActions.filter(action => action.actionUsers && action.actionUsers.length > 0).length,
            unassignedActions: allActions.filter(action => !action.actionUsers || action.actionUsers.length === 0).length,
            actionsWithDueDates: allActions.filter(action => action.actionDate).length,
            overdue: allActions.filter(action => {
                if (!action.actionDate) return false;
                return new Date(action.actionDate) < new Date();
            }).length,
            linkedToAgenda: allActions.filter(action => action.linkedAgenda && action.linkedAgenda.length > 0).length
        };

        return analytics;
    }

    // Utility Functions
    findActionNode(tree, actionId) {
        const searchNode = (node) => {
            if (node.id === actionId) return node;

            for (const child of node.childNodes || []) {
                const found = searchNode(child);
                if (found) return found;
            }

            return null;
        };

        for (const rootNode of tree.childNodes || []) {
            const found = searchNode(rootNode);
            if (found) return found;
        }

        return null;
    }

    getAllActionNodes(tree) {
        const actions = [];

        const collectNodes = (node) => {
            actions.push(node);
            for (const child of node.childNodes || []) {
                collectNodes(child);
            }
        };

        for (const rootNode of tree.childNodes || []) {
            collectNodes(rootNode);
        }

        return actions;
    }

    // Event Handlers
    handleStateChange(detail) {
        console.log('🔄 Meenoe state changed:', detail);
        this.contextCache.set('lastStateChange', {
            timestamp: new Date().toISOString(),
            detail: detail
        });
    }

    handleAgendaChange(detail) {
        console.log('📋 Agenda changed:', detail);
        this.contextCache.set('lastAgendaChange', {
            timestamp: new Date().toISOString(),
            detail: detail
        });
    }

    handleActionsChange(detail) {
        console.log('✅ Actions changed:', detail);
        this.contextCache.set('lastActionsChange', {
            timestamp: new Date().toISOString(),
            detail: detail
        });
    }

    handleUsersChange(detail) {
        console.log('👥 Users changed:', detail);
        this.contextCache.set('lastUsersChange', {
            timestamp: new Date().toISOString(),
            detail: detail
        });
    }

    // Additional utility implementations
    analyzeMeetingStructure() {
        const state = this.getCurrentMeenoeState();
        const agendaAnalytics = this.getAgendaAnalytics();
        const actionAnalytics = this.getActionAnalytics();

        const analysis = {
            overall: {
                completeness: this.calculateCompleteness(state),
                balance: this.calculateBalance(agendaAnalytics, actionAnalytics),
                efficiency: this.calculateEfficiency(state, agendaAnalytics, actionAnalytics)
            },
            recommendations: this.generateRecommendations(state, agendaAnalytics, actionAnalytics),
            strengths: this.identifyStrengths(state, agendaAnalytics, actionAnalytics),
            improvements: this.identifyImprovements(state, agendaAnalytics, actionAnalytics)
        };

        return analysis;
    }

    calculateCompleteness(state) {
        let score = 0;
        let maxScore = 5;

        if (state.name && state.name !== 'Name Your Meenoe Here') score += 1;
        if (state.objective && state.objective !== 'Enter your Meenoe objective or an introduction here') score += 1;
        if (state.counters.users > 0) score += 1;
        if (state.counters.agendaPoints > 0) score += 1;
        if (state.counters.actions > 0) score += 1;

        return Math.round((score / maxScore) * 100);
    }

    calculateBalance(agendaAnalytics, actionAnalytics) {
        const agendaCount = agendaAnalytics.totalItems;
        const actionCount = actionAnalytics.totalActions;

        if (agendaCount === 0 && actionCount === 0) return 0;

        const ratio = agendaCount > 0 ? actionCount / agendaCount : 0;
        const idealRatio = 2; // 2 actions per agenda item is considered balanced

        const balance = Math.max(0, 100 - Math.abs(ratio - idealRatio) * 25);
        return Math.round(balance);
    }

    calculateEfficiency(state, agendaAnalytics, actionAnalytics) {
        let score = 0;
        let factors = 0;

        // Check urgency distribution
        const urgencyTotal = Object.values(agendaAnalytics.urgencyDistribution).reduce((a, b) => a + b, 0);
        if (urgencyTotal > 0) {
            const criticalRatio = agendaAnalytics.urgencyDistribution.critical / urgencyTotal;
            score += criticalRatio < 0.3 ? 25 : 10; // Not too many critical items
            factors += 1;
        }

        // Check action assignment
        if (actionAnalytics.totalActions > 0) {
            const assignmentRatio = actionAnalytics.assignedActions / actionAnalytics.totalActions;
            score += assignmentRatio * 25;
            factors += 1;
        }

        // Check action-agenda linking
        if (actionAnalytics.totalActions > 0) {
            const linkingRatio = actionAnalytics.linkedToAgenda / actionAnalytics.totalActions;
            score += linkingRatio * 25;
            factors += 1;
        }

        // Check due dates
        if (actionAnalytics.totalActions > 0) {
            const dueDateRatio = actionAnalytics.actionsWithDueDates / actionAnalytics.totalActions;
            score += dueDateRatio * 25;
            factors += 1;
        }

        return factors > 0 ? Math.round(score / factors) : 0;
    }

    generateRecommendations(state, agendaAnalytics, actionAnalytics) {
        const recommendations = [];

        if (state.counters.agendaPoints === 0) {
            recommendations.push({
                type: 'agenda',
                priority: 'high',
                message: 'Add agenda items to structure your meeting'
            });
        }

        if (state.counters.actions === 0 && state.counters.agendaPoints > 0) {
            recommendations.push({
                type: 'actions',
                priority: 'medium',
                message: 'Create action items to track follow-ups from agenda discussions'
            });
        }

        if (actionAnalytics.unassignedActions > actionAnalytics.assignedActions) {
            recommendations.push({
                type: 'assignment',
                priority: 'medium',
                message: 'Assign owners to action items for better accountability'
            });
        }

        if (actionAnalytics.actionsWithDueDates < actionAnalytics.totalActions * 0.5) {
            recommendations.push({
                type: 'deadlines',
                priority: 'low',
                message: 'Add due dates to action items for better time management'
            });
        }

        return recommendations;
    }

    identifyStrengths(state, agendaAnalytics, actionAnalytics) {
        const strengths = [];

        if (agendaAnalytics.totalItems > 0) {
            strengths.push('Well-structured agenda');
        }

        if (actionAnalytics.assignedActions > actionAnalytics.unassignedActions) {
            strengths.push('Good action item ownership');
        }

        if (actionAnalytics.linkedToAgenda > actionAnalytics.totalActions * 0.7) {
            strengths.push('Strong agenda-action linkage');
        }

        if (state.counters.users > 1) {
            strengths.push('Collaborative meeting setup');
        }

        return strengths;
    }

    identifyImprovements(state, agendaAnalytics, actionAnalytics) {
        const improvements = [];

        if (agendaAnalytics.urgencyDistribution.critical > agendaAnalytics.totalItems * 0.5) {
            improvements.push('Consider reducing critical urgency items for better focus');
        }

        if (actionAnalytics.overdue > 0) {
            improvements.push('Address overdue action items');
        }

        if (state.counters.files === 0) {
            improvements.push('Consider adding supporting documents');
        }

        return improvements;
    }

    generateMeetingSummary() {
        const state = this.getCurrentMeenoeState();
        const agendaAnalytics = this.getAgendaAnalytics();
        const actionAnalytics = this.getActionAnalytics();

        return {
            meeting: {
                title: state.name,
                objective: state.objective,
                status: state.status,
                participants: state.counters.users
            },
            agenda: {
                totalItems: agendaAnalytics.totalItems,
                urgencyBreakdown: agendaAnalytics.urgencyDistribution,
                discussions: agendaAnalytics.totalThreads
            },
            actions: {
                totalActions: actionAnalytics.totalActions,
                statusBreakdown: actionAnalytics.statusDistribution,
                assigned: actionAnalytics.assignedActions,
                withDeadlines: actionAnalytics.actionsWithDueDates
            },
            files: state.counters.files,
            timestamp: new Date().toISOString()
        };
    }

    extractActionsFromText(text) {
        // Simple action extraction using patterns
        const actionPatterns = [
            /(?:action|todo|task|follow[- ]?up|next step):\s*(.+)/gi,
            /(?:^|\n)\s*[-*]\s*(.+(?:will|should|must|need to).+)/gmi,
            /(?:assign|delegate|responsible for):\s*(.+)/gi
        ];

        const extractedActions = [];

        actionPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(text)) !== null) {
                const actionText = match[1].trim();
                if (actionText.length > 5) { // Filter out very short matches
                    extractedActions.push({
                        text: actionText,
                        confidence: 0.8,
                        source: 'pattern_match'
                    });
                }
            }
        });

        // Remove duplicates
        const uniqueActions = extractedActions.filter((action, index, self) =>
            index === self.findIndex(a => a.text.toLowerCase() === action.text.toLowerCase())
        );

        return {
            extractedActions: uniqueActions,
            totalFound: uniqueActions.length,
            originalText: text
        };
    }

    suggestAgendaStructure(objective, participantCount = 1, duration = 60) {
        const suggestions = [];

        // Basic structure based on meeting duration
        if (duration <= 30) {
            suggestions.push(
                { title: 'Quick Check-in', duration: 5, urgency: 'low' },
                { title: 'Main Topic Discussion', duration: 20, urgency: 'high' },
                { title: 'Action Items & Next Steps', duration: 5, urgency: 'medium' }
            );
        } else if (duration <= 60) {
            suggestions.push(
                { title: 'Welcome & Introductions', duration: 5, urgency: 'low' },
                { title: 'Review Previous Actions', duration: 10, urgency: 'medium' },
                { title: 'Main Discussion Topics', duration: 35, urgency: 'high' },
                { title: 'Action Items & Assignments', duration: 8, urgency: 'medium' },
                { title: 'Next Steps & Closing', duration: 2, urgency: 'low' }
            );
        } else {
            suggestions.push(
                { title: 'Welcome & Agenda Review', duration: 10, urgency: 'low' },
                { title: 'Previous Meeting Follow-up', duration: 15, urgency: 'medium' },
                { title: 'Primary Discussion Topics', duration: duration * 0.5, urgency: 'high' },
                { title: 'Decision Making & Consensus', duration: duration * 0.2, urgency: 'high' },
                { title: 'Action Planning', duration: duration * 0.15, urgency: 'medium' },
                { title: 'Summary & Next Meeting', duration: duration * 0.05, urgency: 'low' }
            );
        }

        // Adjust based on participant count
        if (participantCount > 5) {
            suggestions.unshift({
                title: 'Ground Rules & Participation Guidelines',
                duration: 5,
                urgency: 'medium'
            });
        }

        // Customize based on objective keywords
        const objectiveLower = objective.toLowerCase();
        if (objectiveLower.includes('brainstorm') || objectiveLower.includes('creative')) {
            suggestions.splice(2, 0, {
                title: 'Brainstorming Session',
                duration: Math.max(20, duration * 0.4),
                urgency: 'high'
            });
        }

        if (objectiveLower.includes('decision') || objectiveLower.includes('approve')) {
            suggestions.splice(-2, 0, {
                title: 'Decision Point & Voting',
                duration: 15,
                urgency: 'critical'
            });
        }

        return {
            suggestedStructure: suggestions,
            totalDuration: suggestions.reduce((sum, item) => sum + item.duration, 0),
            participantCount: participantCount,
            objective: objective
        };
    }

    // Placeholder implementations for initialization functions
    initializeMeenoe(config) {
        // This would integrate with the actual Meenoe initialization system
        return { success: true, message: 'Meenoe initialization not yet implemented' };
    }

    updateMeenoeTitle(title) {
        if (window.meenoeState) {
            window.meenoeState.state.name = title;
            // Update UI if title element exists
            const titleElement = document.querySelector('.meenoe-title, #meenoe-title');
            if (titleElement) {
                titleElement.textContent = title;
            }
            return { success: true, newTitle: title };
        }
        throw new Error('Meenoe state system not available');
    }

    updateMeenoeObjective(objective) {
        if (window.meenoeState) {
            window.meenoeState.state.objective = objective;
            // Update UI if objective element exists
            const objectiveElement = document.querySelector('.meenoe-objective, #meenoe-objective');
            if (objectiveElement) {
                objectiveElement.textContent = objective;
            }
            return { success: true, newObjective: objective };
        }
        throw new Error('Meenoe state system not available');
    }

    addParticipant(userDetails) {
        // This would integrate with the actual user management system
        return { success: true, message: 'Participant management not yet implemented', userDetails };
    }

    removeParticipant(userId) {
        // This would integrate with the actual user management system
        return { success: true, message: 'Participant management not yet implemented', userId };
    }

    setMeenoeStatus(status) {
        if (window.meenoeState) {
            window.meenoeState.state.status = status;
            return { success: true, newStatus: status };
        }
        throw new Error('Meenoe state system not available');
    }

    configureMeenoeSettings(settings) {
        // This would integrate with the actual settings system
        return { success: true, message: 'Settings configuration not yet implemented', settings };
    }

    // Helper methods for system availability
    isMeenoeSystemAvailable() {
        return !!(window.meenoeState && window.agendaFlow && window.tree);
    }

    async waitForMeenoeSystem(timeout = 10000) {
        const startTime = Date.now();

        while (!this.isMeenoeSystemAvailable() && (Date.now() - startTime) < timeout) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        return this.isMeenoeSystemAvailable();
    }

    safeCall(fn) {
        try {
            return fn();
        } catch (error) {
            console.warn('Safe call failed:', error);
            return null;
        }
    }
}

// Export for use in other modules
if (typeof window !== 'undefined') {
    window.MeenoeAIIntegration = MeenoeAIIntegration;
}
